import { createContext, useContext, useMemo, type ReactNode } from "react"
import { type KenoConfiguration } from "@betswirl/sdk-core"
import { useKenoConfiguration } from "../hooks/useKenoConfiguration"
import type { TokenWithImage } from "../types/types"

interface KenoConfigContextValue {
  config: KenoConfiguration | undefined
  loading: boolean
  error: Error | null
}

const KenoConfigContext = createContext<KenoConfigContextValue | undefined>(undefined)

interface KenoConfigProviderProps {
  children: ReactNode
  token: TokenWithImage
}

export function KenoConfigProvider({ children, token }: KenoConfigProviderProps) {
  const { config, loading, error } = useKenoConfiguration({
    token,
    query: { enabled: true }
  })

  const value = useMemo(
    () => ({ config, loading, error }),
    [config, loading, error]
  )

  return (
    <KenoConfigContext.Provider value={value}>
      {children}
    </KenoConfigContext.Provider>
  )
}

export function useKenoConfig() {
  const context = useContext(KenoConfigContext)
  if (!context) {
    throw new Error("useKenoConfig must be used within a KenoConfigProvider")
  }
  return context
}

// Optional: Hook that safely returns null outside of provider
export function useKenoConfigSafe() {
  try {
    return useKenoConfig()
  } catch {
    return null
  }
}
