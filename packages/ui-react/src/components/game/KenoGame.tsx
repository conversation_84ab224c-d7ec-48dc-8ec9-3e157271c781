import { CASINO_GAME_TYPE, FORMAT_TYPE, formatRawAmount, KenoBall, Keno, chainById, chainNativeCurrencyToToken } from "@betswirl/sdk-core"
import { useEffect, useState, useMemo } from "react"
import kenoBackground from "../../assets/game/game-background.jpg"
import { useChain } from "../../context/chainContext"
import { useTokenContext } from "../../context/tokenContext"
import { useGameLogic } from "../../hooks/useGameLogic"
import { useKenoConfiguration } from "../../hooks/useKenoConfiguration"
import { useKenoMultipliers } from "../../hooks/useKenoMultipliers"
import { KenoGameDefinition, KenoGameContext } from "../../types/types"
import { GameFrame } from "./GameFrame"
import { KenoGameControls } from "./KenoGameControls"
import { GameConnectWallet } from "./shared/GameConnectWallet"
import { BaseGameProps } from "./shared/types"
import { useGameControls } from "./shared/useGameControls"

const DEFAULT_KENO_SELECTION: KenoBall[] = []

export interface KenoGameProps extends BaseGameProps {}

export function KenoGame({
  theme = "system",
  customTheme,
  backgroundImage = kenoBackground,
  ...props
}: KenoGameProps) {
  const [lastWinningNumbers, setLastWinningNumbers] = useState<KenoBall[]>([])

  // 1. Получаем токен здесь, а не в useGameLogic
  const { selectedToken } = useTokenContext()
  const { appChainId } = useChain()
  const token = useMemo(() => {
    return selectedToken || { ...chainNativeCurrencyToToken(chainById[appChainId].nativeCurrency), image: "" }
  }, [selectedToken, appChainId])

  // 2. Получаем kenoConfig прямо в компоненте
  const { config: kenoConfig, loading: kenoConfigLoading } = useKenoConfiguration({ token })

  // 3. Создаем gameDefinition с помощью useMemo, он будет пересчитываться при смене kenoConfig
  const kenoGameDefinition = useMemo((): KenoGameDefinition<{ game: CASINO_GAME_TYPE.KENO, choice: KenoBall[] }> | undefined => {
    if (!kenoConfig) return undefined

    return {
      gameType: CASINO_GAME_TYPE.KENO,
      defaultSelection: {
        game: CASINO_GAME_TYPE.KENO,
        choice: DEFAULT_KENO_SELECTION,
      },
      getMultiplier: (choice, context) => {
        // Вычисляем количество совпадений, которое дает максимальный множитель
        const maxMultiplierHits = context.kenoConfig.multiplierTable[choice.length]?.length - 1 ?? 0;
        return Keno.getMultiplier(context.kenoConfig, choice.length, maxMultiplierHits)
      },
      encodeInput: (choice, context) => Keno.encodeInput(choice, context.kenoConfig),
    }
  }, [kenoConfig])

  // 4. Создаем контекст для передачи в хуки
  const kenoContext = useMemo((): KenoGameContext | undefined => {
    if (!kenoConfig) return undefined
    return { kenoConfig }
  }, [kenoConfig])

  // 5. Вызываем useGameLogic только когда definition готов
  const gameLogic = kenoGameDefinition ? useGameLogic({
    gameDefinition: kenoGameDefinition,
    backgroundImage,
    kenoContext,
  }) : null

  const themeSettings = { ...baseThemeSettings, theme, customTheme }
  const isControlsDisabled = useGameControls(
    isWalletConnected,
    betStatus,
    isInGameResultState,
    isGamePaused,
  )

  const selectedNumbers = (selection as { game: CASINO_GAME_TYPE.KENO; choice: KenoBall[] }).choice

  const { multipliers } = useKenoMultipliers({
    kenoConfig,
    selectedNumbersCount: selectedNumbers.length,
    houseEdge,
  })

  useEffect(() => {
    if (gameResult?.rolled?.game === CASINO_GAME_TYPE.KENO) {
      setLastWinningNumbers(gameResult.rolled.rolled)
    }
  }, [gameResult])

  const handleNumbersChange = (numbers: KenoBall[]) => {
    if (isControlsDisabled) {
      return
    }
    setSelection({
      game: CASINO_GAME_TYPE.KENO,
      choice: numbers,
    })
  }

  return (
    <GameFrame themeSettings={themeSettings} variant="keno" {...props}>
      <GameFrame.Header title="Keno" connectWalletButton={<GameConnectWallet />} />
      <GameFrame.GameArea variant="keno">
        <GameFrame.InfoButton
          winChance={undefined}
          rngFee={formattedVrfFees}
          targetPayout={formatRawAmount(targetPayoutAmount, token.decimals, FORMAT_TYPE.PRECISE)}
          gasPrice={gasPrice}
          tokenDecimals={token.decimals}
          nativeCurrencySymbol={nativeCurrencySymbol}
        />
        <GameFrame.HistoryButton historyData={gameHistory} onHistoryOpen={refreshHistory} />
        <GameFrame.GameControls>
          {kenoConfig ? (
            <KenoGameControls
              selectedNumbers={selectedNumbers}
              onNumbersChange={handleNumbersChange}
              maxSelections={kenoConfig.maxSelectableBalls}
              biggestSelectableBall={kenoConfig.biggestSelectableBall}
              multipliers={multipliers}
              isDisabled={isControlsDisabled}
              lastGameWinningNumbers={lastWinningNumbers}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-8 w-8 border-2 border-text-on-surface-variant border-t-transparent" />
            </div>
          )}
        </GameFrame.GameControls>
        <GameFrame.ResultWindow gameResult={gameResult} currency={token.symbol} />
      </GameFrame.GameArea>
      <GameFrame.BettingSection
        game={CASINO_GAME_TYPE.KENO}
        betCount={1}
        grossMultiplier={grossMultiplier}
        balance={balance}
        isConnected={isWalletConnected}
        token={token}
        betStatus={betStatus}
        betAmount={betAmount}
        vrfFees={vrfFees}
        onBetAmountChange={handleBetAmountChange}
        onPlayBtnClick={handlePlayButtonClick}
        areChainsSynced={areChainsSynced}
        isGamePaused={isGamePaused}
        hasValidSelection={selectedNumbers.length > 0}
        needsTokenApproval={needsTokenApproval}
        isApprovePending={isApprovePending}
        isApproveConfirming={isApproveConfirming}
        isRefetchingAllowance={isRefetchingAllowance}
        approveError={approveError}
      />
    </GameFrame>
  )
}
