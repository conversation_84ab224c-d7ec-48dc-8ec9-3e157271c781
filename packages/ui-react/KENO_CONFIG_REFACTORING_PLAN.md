# Keno Configuration Refactoring Plan

## Executive Summary

This document outlines a comprehensive refactoring plan to eliminate prop drilling and improve the architecture of the Keno game configuration management in the React SDK. The refactoring will transform the current tightly-coupled implementation into a clean, scalable React Context-based solution.

## Problem Analysis

### Current Issues

1. **Prop Drilling Chain**
   - `useGameLogic` → `usePlaceBet` (parameter)
   - `useGameLogic` → `useBetCalculations` (parameter)
   - `useGameLogic` → `KenoGame` (return value)
   - `KenoGame` → `useKenoMultipliers` (prop)
   - `KenoGame` → `KenoGameControls` (conditional rendering)

2. **Tight Coupling**
   - `useGameLogic` contains Keno-specific logic (lines 134-143, 255-257)
   - Generic hooks require optional Keno parameters
   - Conditional logic scattered across multiple files

3. **Scalability Issues**
   - Adding new games (e.g., Plinko) would require modifying `useGameLogic`
   - Each game-specific configuration would increase complexity

4. **Code Smells**
   - Optional parameters: `kenoConfig?: KenoConfiguration`
   - Null checks throughout the codebase
   - Mixed responsibilities in `useGameLogic`

## Proposed Solution: React Context Architecture

### High-Level Architecture

```mermaid
graph TD
    A[KenoGame Component] --> B[KenoConfigProvider]
    B --> C[KenoGameContent]
    C --> D[useKenoConfig hook]
    C --> E[KenoGameControls]
    C --> F[useBetCalculations]
    C --> G[usePlaceBet]
    C --> H[useKenoMultipliers]
    
    D --> I[Direct Context Access]
    E --> I
    F --> I
    G --> I
    H --> I
    
    J[useGameLogic] -->|No Keno Dependencies| K[Generic Game Logic Only]
```

### Benefits

1. **Eliminates Prop Drilling**: Direct context access from any component/hook
2. **Better Separation of Concerns**: Game-specific logic isolated from generic logic
3. **Improved Scalability**: Easy to add new game-specific contexts
4. **Cleaner Code**: No optional parameters or scattered null checks
5. **Better Performance**: Memoized context values prevent unnecessary re-renders
6. **Easier Testing**: Mockable context providers for unit tests

## Implementation Plan

### Phase 1: Create Context Infrastructure

#### 1.1 Create `src/context/kenoConfigContext.tsx`

```typescript
import { createContext, useContext, useMemo, type ReactNode } from "react"
import { type KenoConfiguration } from "@betswirl/sdk-core"
import { useKenoConfiguration } from "../hooks/useKenoConfiguration"
import type { TokenWithImage } from "../types/types"

interface KenoConfigContextValue {
  config: KenoConfiguration | undefined
  loading: boolean
  error: Error | null
}

const KenoConfigContext = createContext<KenoConfigContextValue | undefined>(undefined)

interface KenoConfigProviderProps {
  children: ReactNode
  token: TokenWithImage
}

export function KenoConfigProvider({ children, token }: KenoConfigProviderProps) {
  const { config, loading, error } = useKenoConfiguration({ 
    token,
    query: { enabled: true }
  })

  const value = useMemo(
    () => ({ config, loading, error }), 
    [config, loading, error]
  )

  return (
    <KenoConfigContext.Provider value={value}>
      {children}
    </KenoConfigContext.Provider>
  )
}

export function useKenoConfig() {
  const context = useContext(KenoConfigContext)
  if (!context) {
    throw new Error("useKenoConfig must be used within a KenoConfigProvider")
  }
  return context
}

// Optional: Hook that safely returns null outside of provider
export function useKenoConfigSafe() {
  try {
    return useKenoConfig()
  } catch {
    return null
  }
}
```

### Phase 2: Refactor KenoGame Component

#### 2.1 Update `src/components/game/KenoGame.tsx`

```typescript
export function KenoGame({ 
  theme = "system",
  customTheme,
  backgroundImage = kenoBackground,
  ...props 
}: KenoGameProps) {
  const gameLogic = useGameLogic({
    gameType: CASINO_GAME_TYPE.KENO,
    defaultSelection: { 
      game: CASINO_GAME_TYPE.KENO, 
      choice: DEFAULT_KENO_SELECTION 
    },
    backgroundImage,
  })

  return (
    <KenoConfigProvider token={gameLogic.token}>
      <KenoGameContent 
        {...props}
        gameLogic={gameLogic}
        theme={theme}
        customTheme={customTheme}
      />
    </KenoConfigProvider>
  )
}

function KenoGameContent({ gameLogic, theme, customTheme, ...props }) {
  const [lastWinningNumbers, setLastWinningNumbers] = useState<KenoBall[]>([])
  const { config: kenoConfig, loading: kenoConfigLoading } = useKenoConfig()
  
  const {
    isWalletConnected,
    balance,
    token,
    // ... other destructured values from gameLogic
  } = gameLogic

  const selectedNumbers = (gameLogic.selection as { 
    game: CASINO_GAME_TYPE.KENO; 
    choice: KenoBall[] 
  }).choice

  const { multipliers } = useKenoMultipliers({
    selectedNumbersCount: selectedNumbers.length,
    houseEdge: gameLogic.houseEdge,
  })

  // ... rest of component logic

  return (
    <GameFrame themeSettings={themeSettings} variant="keno" {...props}>
      {/* ... existing JSX */}
      <GameFrame.GameControls>
        {kenoConfigLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-text-on-surface-variant border-t-transparent" />
          </div>
        ) : kenoConfig ? (
          <KenoGameControls
            selectedNumbers={selectedNumbers}
            onNumbersChange={handleNumbersChange}
            maxSelections={kenoConfig.maxSelectableBalls}
            biggestSelectableBall={kenoConfig.biggestSelectableBall}
            multipliers={multipliers}
            isDisabled={isControlsDisabled}
            lastGameWinningNumbers={lastWinningNumbers}
          />
        ) : null}
      </GameFrame.GameControls>
      {/* ... rest of JSX */}
    </GameFrame>
  )
}
```

### Phase 3: Update Hooks to Use Context

#### 3.1 Refactor `src/hooks/useBetCalculations.ts`

```typescript
import { useKenoConfigSafe } from "../context/kenoConfigContext"

interface UseBetCalculationsProps {
  selection: GameChoice
  houseEdge: number
  betAmount: bigint | undefined
  betCount: number | undefined
  // Remove kenoConfig parameter
}

export function useBetCalculations({
  selection,
  houseEdge,
  betAmount,
  betCount = 1,
}: UseBetCalculationsProps): UseBetCalculationsResult {
  // Only get keno config if it's a keno game
  const kenoContext = selection.game === CASINO_GAME_TYPE.KENO 
    ? useKenoConfigSafe() 
    : null
    
  const kenoConfig = kenoContext?.config

  const grossMultiplier = useMemo(
    () => getMultiplierForGame(selection, kenoConfig),
    [selection, kenoConfig],
  )
  
  // Rest of the logic remains the same
  const totalBetAmount = useMemo(
    () => (betAmount && betAmount > 0n ? betAmount * BigInt(betCount) : 0n),
    [betAmount, betCount],
  )

  const { grossPayout, netPayout, betSwirlFees, netMultiplier, formattedNetMultiplier } =
    getPayoutDetails(betAmount ?? 0n, betCount, grossMultiplier, houseEdge)

  return {
    houseEdge,
    totalBetAmount,
    grossMultiplier,
    netMultiplier,
    grossPayout,
    netPayout,
    betSwirlFees,
    formattedNetMultiplier,
  }
}
```

#### 3.2 Refactor `src/hooks/usePlaceBet.ts`

```typescript
import { useKenoConfigSafe } from "../context/kenoConfigContext"

export function usePlaceBet(
  game: CASINO_GAME_TYPE,
  token: TokenWithImage,
  refetchBalance: () => void,
  // Remove kenoConfig parameter
): IUsePlaceBetReturn {
  // Get keno config from context only if it's a keno game
  const kenoContext = game === CASINO_GAME_TYPE.KENO 
    ? useKenoConfigSafe() 
    : null
    
  const kenoConfig = kenoContext?.config

  // ... existing hook setup

  const placeBet = useCallback(
    async (betAmount: bigint, choice: GameChoice) => {
      resetBetState()
      setCurrentBetAmount(betAmount)

      const encodedInput = _encodeGameInput(choice, kenoConfig)
      
      // ... rest of the function
    },
    [
      game,
      resetBetState,
      publicClient,
      appChainId,
      connectedAddress,
      wagerWriteHook.writeContract,
      estimateVrfFeesWagmiHook.refetch,
      formattedVrfFees,
      vrfFees,
      gasPrice,
      token,
      affiliate,
      kenoConfig, // Now from context
    ],
  )

  // ... rest of the hook
}
```

#### 3.3 Refactor `src/hooks/useKenoMultipliers.ts`

```typescript
import { useKenoConfig } from "../context/kenoConfigContext"

interface UseKenoMultipliersProps {
  selectedNumbersCount: number
  houseEdge: number
  // Remove kenoConfig parameter
}

export function useKenoMultipliers({
  selectedNumbersCount,
  houseEdge,
}: UseKenoMultipliersProps): UseKenoMultipliersResult {
  const { config: kenoConfig } = useKenoConfig()
  
  const multipliers = useMemo(() => {
    if (!kenoConfig || selectedNumbersCount === 0) {
      return []
    }

    const rawMultipliers = kenoConfig.multiplierTable[selectedNumbersCount]
    if (!rawMultipliers) {
      return []
    }

    return rawMultipliers
      .map((_, index) => {
        const multiplier = getFormattedNetMultiplier(
          Keno.getMultiplier(kenoConfig, selectedNumbersCount, index),
          houseEdge,
        )
        const winChance = Keno.getWinChancePercent(kenoConfig, selectedNumbersCount, index)

        return {
          multiplier,
          winChance,
        }
      })
      .reverse()
  }, [kenoConfig, selectedNumbersCount, houseEdge])

  return {
    multipliers,
    isLoading: !kenoConfig,
  }
}
```

### Phase 4: Clean Up useGameLogic

#### 4.1 Remove Keno-specific code from `src/hooks/useGameLogic.ts`

```typescript
export function useGameLogic({
  gameType,
  defaultSelection,
  backgroundImage,
}: UseGameLogicProps): UseGameLogicResult {
  // ... existing code

  // REMOVE: Lines 134-143 (kenoConfiguration call)
  // const kenoConfiguration = useKenoConfiguration({...})
  // const kenoConfigResult = gameType === CASINO_GAME_TYPE.KENO ? ...

  // UPDATE: Line 145 - Remove kenoConfig parameter
  const { placeBet, betStatus, gameResult, resetBetState, vrfFees, formattedVrfFees, gasPrice } =
    usePlaceBet(gameType, token, refetchBalance) // No kenoConfig

  // UPDATE: Line 163-169 - Remove kenoConfig parameter
  const { netPayout, formattedNetMultiplier, grossMultiplier } = useBetCalculations({
    selection,
    houseEdge,
    betAmount,
    betCount: 1,
    // Remove: kenoConfig: kenoConfigResult.config,
  })

  // ... rest of the hook

  return {
    // ... existing return values
    // REMOVE: kenoConfig: kenoConfigResult.config,
    // REMOVE: kenoConfigLoading: kenoConfigResult.loading,
    // REMOVE: kenoConfigError: kenoConfigResult.error,
  }
}
```

#### 4.2 Update UseGameLogicResult interface

```typescript
interface UseGameLogicResult {
  // ... existing fields
  // Remove these fields:
  // kenoConfig?: KenoConfiguration
  // kenoConfigLoading?: boolean
  // kenoConfigError?: Error | null
}
```

## Migration Strategy

### Step-by-Step Migration

1. **Phase 1: Context Infrastructure (No Breaking Changes)**
   - Create `kenoConfigContext.tsx`
   - Test context provider in isolation

2. **Phase 2: Update KenoGame Component**
   - Wrap with KenoConfigProvider
   - Split into container/content pattern
   - Test Keno game functionality

3. **Phase 3: Update Hooks One by One**
   - Start with `useKenoMultipliers` (simplest)
   - Then `useBetCalculations`
   - Finally `usePlaceBet` (most complex)
   - Test after each hook update

4. **Phase 4: Clean Up useGameLogic**
   - Remove all Keno-specific code
   - Update type definitions
   - Full regression testing

### Testing Strategy

1. **Unit Tests**
   - Test context provider with mock data
   - Test hooks with mock context
   - Test error boundaries

2. **Integration Tests**
   - Test full Keno game flow
   - Test bet placement with various selections
   - Test multiplier calculations

3. **Edge Cases**
   - Context used outside provider
   - Loading states
   - Error states
   - Token changes

## Potential Challenges & Solutions

### 1. Conditional Hook Usage

**Challenge**: React hooks must be called unconditionally
**Solution**: Use `useKenoConfigSafe` that returns null outside provider

### 2. SSR Compatibility

**Challenge**: Context might not be available during SSR
**Solution**: Add appropriate checks and fallbacks

### 3. Performance Considerations

**Challenge**: Context updates might cause unnecessary re-renders
**Solution**: Memoize context value and use selective subscriptions

## Future Extensibility

This pattern can be easily extended for other games:

```typescript
// For future Plinko game
export function PlinkoGame() {
  const gameLogic = useGameLogic({
    gameType: CASINO_GAME_TYPE.PLINKO,
    // ...
  })

  return (
    <PlinkoConfigProvider token={gameLogic.token}>
      <PlinkoGameContent {...gameLogic} />
    </PlinkoConfigProvider>
  )
}
```

## Conclusion

This refactoring will significantly improve code maintainability, scalability, and developer experience. The React Context pattern is a proven solution for this type of prop drilling problem and aligns with React best practices.

### Expected Outcomes

- ✅ Cleaner, more maintainable code
- ✅ Better separation of concerns
- ✅ Easier to add new games
- ✅ Improved type safety
- ✅ Better performance
- ✅ Easier testing

### Timeline Estimate

- Phase 1: 1-2 hours
- Phase 2: 2-3 hours
- Phase 3: 3-4 hours
- Phase 4: 1-2 hours
- Testing: 2-3 hours

**Total: 9-14 hours**
